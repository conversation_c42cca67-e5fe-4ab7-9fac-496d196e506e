<%
# Copyright (C) 2013 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHA<PERSON>ABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>

<div class="announcements_list">
  <div class="announcements-header sticky-header">
    <div class="h2 shared-space">
      <h2>
        <%= t('announcements', "Announcements") %>
      </h2>
    </div>
  </div>

  <div class="announcements-content scrollable-content">
    <ul class="right-side-list announcements">
    <% if @global_announcements&.any? || @course_announcements&.any? %>
      <% 
        # Combine global and course announcements into a single array
        all_announcements = []
        
        # Add global announcements (already filtered by controller)
        @global_announcements&.each do |announcement|
          all_announcements << {
            type: 'global',
            announcement: announcement,
            title: announcement.subject,
            context_name: t('Global'),
            date: announcement.start_at&.strftime("%b %d"),
            url: dashboard_url,
            category: announcement.respond_to?(:category) && announcement.category.present? ? announcement.category : nil,
            priority: announcement.respond_to?(:priority) && announcement.priority.present? ? announcement.priority : nil
          }
        end

        # Add course announcements (already filtered by enrollment in controller)
        @course_announcements&.each do |announcement|
          all_announcements << {
            type: 'course',
            announcement: announcement,
            title: announcement.title,
            context_name: announcement.context&.name,
            date: announcement.posted_at&.strftime("%b %d"),
            url: context_url(announcement.context, :context_discussion_topic_url, announcement),
            category: announcement.respond_to?(:category) && announcement.category.present? ? announcement.category : nil,
            priority: announcement.respond_to?(:priority) && announcement.priority.present? ? announcement.priority : nil
          }
        end
        
        # Define category weights for sorting
        category_weights = {
          'Urgent' => 4,
          'Event' => 3,
          'Reminder' => 2,
          'General' => 1
        }
        
        # Sort announcements by category weight and priority
        # Items without category or priority go to the end
        all_announcements.sort_by! do |a|
          [
            a[:category] ? (-1 * (category_weights[a[:category]] || 0)) : -999, # Items without category go last
            a[:priority] ? (-1 * a[:priority]) : -999                           # Items without priority go last
          ]
        end
      %>
      
      <!-- Combined and Sorted Announcements -->
      <% all_announcements.each do |announcement_data| %>
        <%
          # Determine if announcement is urgent
          is_urgent = announcement_data[:category]&.downcase == 'urgent'

          # Build CSS classes for styling
          css_classes = ['event']
          css_classes << announcement_data[:category].downcase if announcement_data[:category]
          css_classes << 'urgent-announcement' if is_urgent
          css_classes << 'low-priority-announcement' if announcement_data[:category] && !is_urgent
        %>
        <li class="<%= css_classes.join(' ') %>">
          <a href="<%= announcement_data[:url] %>" data-track-category="dashboard" data-track-label="announcement">
            <div class="announcement-icon-wrapper">
              <i class="icon-announcement" aria-label="<%= t('Announcement') %>"></i>
              <% if is_urgent %>
                <span class="urgent-indicator" aria-label="<%= t('Urgent') %>">!</span>
              <% end %>
            </div>
            <div class="event-details">
              <div class="announcement-header">
                <b class="event-details__title"><%= announcement_data[:title] %></b>
              </div>
              <% if announcement_data[:category] %>
                <p class="announcement-category margin-bottom-5">
                  <span class="category-label <%= announcement_data[:category].downcase %>-category">
                    <%= announcement_data[:category] %>
                  </span>
                  <% if !is_urgent %>
                    <span class="low-priority-label"><%= t('(Low Priority)') %></span>
                  <% end %>
                </p>
              <% end %>
              <% if announcement_data[:priority] %>
                <span class="priority-badge priority-<%= announcement_data[:priority] %>">
                  <%= t('Priority') %> <%= announcement_data[:priority] %>
                </span>
              <% end %>
              <p><%= announcement_data[:context_name] %></p>
              <p><%= announcement_data[:date] %></p>
            </div>
          </a>
        </li>
      <% end %>
    <% else %>
      <li>
        <small><%= t('no_announcements', "No announcements at this time") %></small>
      </li>
    <% end %>
    </ul>
  </div>
</div>
